<script setup>
import { ref, onMounted } from 'vue';
import VideoInput from './components/VideoInput.vue';
import VideoPreview from './components/VideoPreview.vue';
import ExampleVideos from './components/ExampleVideos.vue';
import SocialProof from './components/SocialProof.vue';
import Navbar from './components/Navbar.vue';
import FinalTabsContainer from './components/FinalTabsContainer.vue';
import { checkApiConnectivity } from './debug.js';

const selectedVideoId = ref('');
const videoPreviewRef = ref(null);
const currentVideoTime = ref(0);
const examplesCollapsed = ref(false);
const userVideos = ref([]);
const userVideoDetails = ref({});
const currentLanguage = ref('en'); // Track the currently selected language

function handleVideoSelected(videoId) {
  console.log(`🎯 App: handleVideoSelected called with videoId:`, videoId);
  console.log(`🎯 App: Previous selectedVideoId:`, selectedVideoId.value);

  selectedVideoId.value = videoId;

  console.log(`🎯 App: New selectedVideoId:`, selectedVideoId.value);
  console.log(`🎯 App: Video content should now be visible`);

  // Collapse example videos when a video is selected
  examplesCollapsed.value = true;
  console.log(`🎯 App: Collapsed example videos`);

  // Scroll to video content when a video is selected
  setTimeout(() => {
    const videoContent = document.querySelector('.video-content');
    if (videoContent) {
      console.log(`🎯 App: Scrolling to video content`);
      videoContent.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    } else {
      console.log(`🎯 App: Video content element not found, trying alternative selector`);
      // Fallback: try to find the tabs container or video preview
      const tabsContainer = document.querySelector('.tabs-container') ||
                           document.querySelector('[class*="tabs"]') ||
                           document.querySelector('.card');
      if (tabsContainer) {
        tabsContainer.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }
  }, 500); // Longer delay to ensure content is rendered
}

function handlePlayVideo(videoId) {
  selectedVideoId.value = videoId;

  // Collapse example videos when playing a video
  examplesCollapsed.value = true;
  console.log(`🎬 App: Collapsed example videos for play video`);

  // Scroll to video content when playing from the gallery
  setTimeout(() => {
    const videoContent = document.querySelector('.video-content');
    if (videoContent) {
      videoContent.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    } else {
      // Fallback: try to find the tabs container or video preview
      const tabsContainer = document.querySelector('.tabs-container') ||
                           document.querySelector('[class*="tabs"]') ||
                           document.querySelector('.card');
      if (tabsContainer) {
        tabsContainer.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }
  }, 500);
}

function handleAddVideo(videoId) {
  console.log("➕ App: handleAddVideo called with videoId:", videoId);
  console.log("➕ App: Current userVideos:", userVideos.value);

  // Add the video to userVideos array if it's not already there and it's valid
  if (videoId && !userVideos.value.includes(videoId)) {
    // Add basic details immediately
    userVideoDetails.value[videoId] = {
      title: 'Video ' + videoId,
      thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
    };

    // Add to user videos array at the end (so new videos appear on the right)
    userVideos.value.push(videoId);

    // Set as selected video
    selectedVideoId.value = videoId;

    // Try to fetch more details in the background
    fetchVideoDetails(videoId);
  }

  // Collapse example videos when user adds their own video
  examplesCollapsed.value = true;
  console.log("➕ App: Collapsed example videos");

  // Scroll to the transcript/video content section after a short delay
  setTimeout(() => {
    const videoContent = document.querySelector('.video-content');
    if (videoContent) {
      console.log("➕ App: Scrolling to video content section");
      videoContent.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    } else {
      console.log("➕ App: Video content element not found, trying alternative selector");
      // Fallback: try to find the tabs container or video preview
      const tabsContainer = document.querySelector('.tabs-container') ||
                           document.querySelector('[class*="tabs"]') ||
                           document.querySelector('.card');
      if (tabsContainer) {
        tabsContainer.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }
  }, 500); // Longer delay to ensure the video content is fully rendered
}

// Separate function to fetch video details
async function fetchVideoDetails(videoId) {
  // First try the oEmbed API directly as it's more reliable for titles
  try {
    const ytResponse = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`);
    const ytData = await ytResponse.json();

    if (ytData && ytData.title) {
      userVideoDetails.value[videoId] = {
        title: ytData.title,
        thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
      };
      console.log('Updated video details from oEmbed (primary):', videoId, ytData.title);
      return; // Exit early if we got the title
    }
  } catch (ytError) {
    console.error('Error fetching video details from YouTube oEmbed (primary):', ytError);
    // Continue to try the backend API
  }

  // If oEmbed fails, try our backend API
  try {
    // Fetch video details from YouTube API via our backend
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${API_URL}/video/${videoId}`);
    const data = await response.json();

    // The API returns the full YouTube API response
    if (data && data.snippet && data.snippet.title) {
      // Update video details with the actual title from YouTube
      userVideoDetails.value[videoId] = {
        title: data.snippet.title,
        thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
      };
      console.log('Updated video details from backend API:', videoId, data.snippet.title);
    }
  } catch (error) {
    console.error('Error fetching video details from backend API:', error);
  }
}

function handleTimeUpdate(time) {
  currentVideoTime.value = time;
}

function handleSeekToTime(time, forcePlay = false) {
  console.log('App: handleSeekToTime called with time:', time, 'forcePlay:', forcePlay, 'for video:', selectedVideoId.value);

  if (videoPreviewRef.value) {
    // Ensure the video player is ready before seeking
    setTimeout(() => {
      // Pass the forcePlay flag to ensure autoplay when clicking on timestamps
      videoPreviewRef.value.seekToTime(time, forcePlay);
    }, 100);
  } else {
    console.warn('App: videoPreviewRef is not available');
  }
}

// Handle subtitle loading state changes
function handleSubtitlesLoadingState(isLoading) {
  console.log('App: Subtitles loading state changed to:', isLoading);

  if (videoPreviewRef.value) {
    // Update the video player's subtitle loading state
    videoPreviewRef.value.setSubtitleLoadingState(isLoading);
  } else {
    console.warn('App: videoPreviewRef is not available for subtitle loading state update');
  }
}

// Handle language changes from the FinalTabsContainer
function handleLanguageChange(languageCode) {
  console.log('App: Language changed to:', languageCode);
  currentLanguage.value = languageCode;
}



function toggleExamples() {
  examplesCollapsed.value = !examplesCollapsed.value;
}

// Check API connectivity when the app mounts
onMounted(async () => {
  console.log('App mounted, checking API connectivity...');
  const isConnected = await checkApiConnectivity();
  console.log('API connectivity check result:', isConnected);
});
</script>

<template>
  <div class="min-h-screen bg-base-200 flex flex-col">
    <!-- Navigation Bar -->
    <Navbar />

    <div class="w-[85%] mx-auto px-4 py-8 flex-grow">
      <main class="space-y-8">
        <!-- Hero Section with Input and Examples -->
        <div class="py-6">
          <VideoInput
            @video-selected="handleVideoSelected"
            @play-video="handlePlayVideo"
            @add-video="handleAddVideo"
            :examplesCollapsed="examplesCollapsed"
            :userVideos="userVideos"
            :userVideoDetails="userVideoDetails"
            :selectedVideoId="selectedVideoId"
            @toggle-examples="toggleExamples"
          />
        </div>

        <!-- Video Content - Two Column Layout with Separate Cards -->
        <div v-if="selectedVideoId" class="flex flex-col md:flex-row gap-6 video-content">
          <!-- Left Card - Video and Transcript Actions -->
          <div class="md:w-2/5">
            <div class="card bg-base-100 shadow-md h-full">
              <div class="card-body">
                <VideoPreview
                  ref="videoPreviewRef"
                  :videoId="selectedVideoId"
                  @time-update="handleTimeUpdate"
                />


              </div>
            </div>
          </div>

          <!-- Right Card - Transcript and Tools -->
          <div class="md:w-3/5">
            <div class="card bg-base-100 shadow-md h-full">
              <div class="card-body p-4">
                <FinalTabsContainer
                  :videoId="selectedVideoId"
                  @seek-to-time="handleSeekToTime"
                  @subtitles-loading-state="handleSubtitlesLoadingState"
                  @language-change="handleLanguageChange"
                />
              </div>
            </div>
          </div>
        </div>



        <!-- Social Proof Section removed as requested -->
      </main>

      <!-- Footer -->
      <footer class="footer footer-center p-4 text-base-content/70 mt-12 border-t border-base-300">
        <p>&copy; {{ new Date().getFullYear() }} YouTube Subtitle Viewer</p>
      </footer>
    </div>
  </div>
</template>
