<script setup>
import { ref, computed } from 'vue';
import axios from 'axios';
import MinimalSocialProof from './MinimalSocialProof.vue';
import UserTrust from './UserTrust.vue';

const props = defineProps({
  examplesCollapsed: {
    type: Boolean,
    default: false
  },
  userVideos: {
    type: Array,
    default: () => []
  },
  userVideoDetails: {
    type: Object,
    default: () => ({})
  },
  selectedVideoId: {
    type: String,
    default: ''
  }
});

const videoUrl = ref('');
const error = ref('');
const videoId = ref('');

// Emit events to parent component
const emit = defineEmits(['video-selected', 'toggle-examples', 'play-video', 'add-video']);

// Debug: Log component mount
console.log('🚀 VideoInput component mounted');

// Validate YouTube URL
const isValidUrl = computed(() => {
  if (!videoUrl.value) return false;

  // Simple YouTube URL validation
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
  return youtubeRegex.test(videoUrl.value);
});

// Extract video ID from URL
const extractVideoId = (url) => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);
  return (match && match[2].length === 11) ? match[2] : null;
};

// Process the URL when button is clicked
const processUrl = () => {
  if (!isValidUrl.value) {
    error.value = 'Please enter a valid YouTube URL';
    return;
  }

  const extractedId = extractVideoId(videoUrl.value);
  if (!extractedId) {
    error.value = 'Could not extract video ID from URL';
    return;
  }

  error.value = '';

  // Emit add-video event with the extracted ID
  console.log("➕ VideoInput: Emitting add-video with ID:", extractedId);
  emit('add-video', extractedId);
  console.log("➕ VideoInput: add-video event emitted successfully");

  // Clear the input field
  videoUrl.value = '';
};

// Toggle examples visibility
const toggleExamples = () => {
  emit('toggle-examples');
};

// Select an example video
const selectExampleVideo = (videoId) => {
  console.log(`🎬 VideoInput: Selecting example video: ${videoId}`);
  console.log(`🎬 VideoInput: Emitting 'video-selected' event with videoId:`, videoId);
  emit('video-selected', videoId);
  console.log(`🎬 VideoInput: Event emitted successfully`);
};
</script>

<template>
  <div class="w-full mx-auto text-center">
    <!-- Hero Section -->
    <div class="hero-section mb-8">
      <!-- Main Headline -->
      <div class="mb-6">
        <h1 class="text-4xl md:text-5xl font-black mb-3 bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent leading-tight">
          Transform YouTube Videos Into Content
        </h1>
        <p class="text-base md:text-lg text-base-content/70 max-w-3xl mx-auto leading-relaxed mb-6">
          Download transcripts, get translations, generate summaries, write descriptions, social posts or get study cards.
        </p>
      </div>

      <!-- Transformation Process Visualization -->
      <div class="transformation-process mb-6">
        <div class="flex flex-col lg:flex-row items-center justify-center gap-6 max-w-5xl mx-auto">

          <!-- Input: YouTube Video -->
          <div class="input-section flex-shrink-0">
            <div class="video-input-visual">
              <div class="video-thumbnail">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </div>
              <p class="text-sm font-medium text-base-content mt-2">YouTube Video</p>
            </div>
          </div>

          <!-- Arrow -->
          <div class="arrow-section flex-shrink-0">
            <div class="transform-arrow">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </div>
            <p class="text-xs text-primary font-medium mt-1">AI Processing</p>
          </div>

          <!-- Output: 6 Content Types -->
          <div class="output-section flex-1">
            <div class="content-grid">
              <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                <!-- Transcripts -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-blue-500 to-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p class="content-label">Transcripts</p>
                </div>

                <!-- Summaries -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-green-500 to-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <p class="content-label">Summaries</p>
                </div>

                <!-- Quotes -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-purple-500 to-purple-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                  </div>
                  <p class="content-label">Quotes</p>
                </div>

                <!-- Descriptions -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-orange-500 to-orange-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </div>
                  <p class="content-label">Descriptions</p>
                </div>

                <!-- Social Posts -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-pink-500 to-pink-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                  </div>
                  <p class="content-label">Social Posts</p>
                </div>

                <!-- Study Cards -->
                <div class="content-item">
                  <div class="content-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <p class="content-label">Study Cards</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Input Field Section -->
      <div class="input-section mb-6">
        <div class="form-control w-full max-w-3xl mx-auto">

          <!-- Design Selector (Temporary) -->
          <div class="design-selector mb-6 p-4 bg-info/10 rounded-lg border border-info/20">
            <h4 class="font-bold text-center mb-3">🎨 Choose Your Preferred Design:</h4>
            <div class="flex flex-wrap justify-center gap-2">
              <button class="btn btn-sm btn-primary" onclick="showDesign(1)">Option 1: Badges</button>
              <button class="btn btn-sm btn-secondary" onclick="showDesign(2)">Option 2: Tabs</button>
              <button class="btn btn-sm btn-accent" onclick="showDesign(3)">Option 3: Steps</button>
              <button class="btn btn-sm btn-warning" onclick="showDesign(4)">Option 4: Toggle</button>
              <button class="btn btn-sm btn-success" onclick="showDesign(5)">Option 5: Radio Cards</button>
            </div>
          </div>

          <!-- Design Option 1: Card-based with badges -->
          <div class="design-option-1 own-video-section mb-6" style="display: block;">
            <div class="input-card">
              <div class="card-badge">
                <span class="badge badge-primary badge-lg">Option 1</span>
              </div>
              <div class="section-header mb-3">
                <h3 class="text-lg font-bold text-base-content">Add Your Own Video</h3>
                <p class="text-sm text-base-content/70">Paste any YouTube URL to get started</p>
              </div>

            <div class="main-input-container">
              <div class="flex flex-col sm:flex-row gap-3">
                <input
                  type="text"
                  v-model="videoUrl"
                  placeholder="Paste YouTube URL here"
                  @keyup.enter="processUrl"
                  class="input input-bordered input-lg w-full focus:outline-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                />
                <button
                  class="btn btn-primary btn-lg font-bold whitespace-nowrap shadow-lg hover:shadow-xl transition-all duration-200"
                  @click="processUrl"
                >
                  Transcribe now
                </button>
              </div>
            </div>
            </div>
          </div>

          <!-- Divider with "OR" -->
          <div class="divider text-base font-medium text-base-content/80 my-6">OR</div>

          <!-- Example Videos Section -->
          <div class="example-videos-section">
            <div class="input-card">
              <div class="card-badge">
                <span class="badge badge-secondary badge-lg">Option 1</span>
              </div>
              <div class="section-header mb-4">
                <h3 class="text-lg font-bold text-base-content">Try Example Videos</h3>
                <p class="text-sm text-base-content/70">Test the app with these pre-loaded videos</p>
              </div>

            <div class="grid grid-cols-2 md:grid-cols-5 gap-3 max-w-4xl mx-auto">
              <!-- Example Video 1: Dunning-Kruger -->
              <div
                class="example-video-card"
                :class="{ 'selected': selectedVideoId === 'TT81fe2IobI' }"
                @click="selectExampleVideo('TT81fe2IobI')"
              >
                <div class="video-thumbnail">
                  <img
                    src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg"
                    alt="Dunning-Kruger Effect"
                    class="w-full h-full object-cover"
                  />
                  <div class="play-overlay">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <p class="video-title">Dunning-Kruger Effect</p>
              </div>

              <!-- Example Video 2: Procrastination -->
              <div
                class="example-video-card"
                :class="{ 'selected': selectedVideoId === 'arj7oStGLkU' }"
                @click="selectExampleVideo('arj7oStGLkU')"
              >
                <div class="video-thumbnail">
                  <img
                    src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg"
                    alt="Procrastination TED Talk"
                    class="w-full h-full object-cover"
                  />
                  <div class="play-overlay">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <p class="video-title">Procrastination TED Talk</p>
              </div>

              <!-- Example Video 3: Quantum Computing -->
              <div
                class="example-video-card"
                :class="{ 'selected': selectedVideoId === 'KpVPST_P4W8' }"
                @click="selectExampleVideo('KpVPST_P4W8')"
              >
                <div class="video-thumbnail">
                  <img
                    src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg"
                    alt="Quantum Computing"
                    class="w-full h-full object-cover"
                  />
                  <div class="play-overlay">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <p class="video-title">Quantum Computing</p>
              </div>

              <!-- Example Video 4: Business Growth -->
              <div
                class="example-video-card"
                :class="{ 'selected': selectedVideoId === 'pqWUuYTcG-o' }"
                @click="selectExampleVideo('pqWUuYTcG-o')"
              >
                <div class="video-thumbnail">
                  <img
                    src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg"
                    alt="Business Growth"
                    class="w-full h-full object-cover"
                  />
                  <div class="play-overlay">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <p class="video-title">Business Growth</p>
              </div>

              <!-- Example Video 5: Steve Jobs Stanford -->
              <div
                class="example-video-card"
                :class="{ 'selected': selectedVideoId === 'UF8uR6Z6KLc' }"
                @click="selectExampleVideo('UF8uR6Z6KLc')"
              >
                <div class="video-thumbnail">
                  <img
                    src="https://i.ytimg.com/vi/UF8uR6Z6KLc/hqdefault.jpg"
                    alt="Steve Jobs Stanford"
                    class="w-full h-full object-cover"
                  />
                  <div class="play-overlay">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                <p class="video-title">Steve Jobs Stanford</p>
              </div>
            </div>
            </div>
          </div>

          <!-- Design Option 2: Tab-style with icons -->
          <div class="design-option-2" style="display: none;">
            <div class="tabs tabs-boxed mb-6 bg-base-200">
              <a class="tab tab-active" onclick="switchTab2('your-video', this)">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16l4-4 4 4V4z" />
                </svg>
                Your Video
              </a>
              <a class="tab" onclick="switchTab2('examples', this)">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Examples
              </a>
            </div>

            <div id="tab2-your-video" class="tab-content">
              <div class="main-input-container">
                <div class="flex flex-col sm:flex-row gap-3">
                  <input type="text" v-model="videoUrl" placeholder="Paste YouTube URL here" class="input input-bordered input-lg w-full" />
                  <button class="btn btn-primary btn-lg font-bold" @click="processUrl">Transcribe now</button>
                </div>
              </div>
            </div>

            <div id="tab2-examples" class="tab-content" style="display: none;">
              <div class="grid grid-cols-2 md:grid-cols-5 gap-3 max-w-4xl mx-auto">
                <!-- Example videos for option 2 -->
                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'TT81fe2IobI' }" @click="selectExampleVideo('TT81fe2IobI')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg" alt="Dunning-Kruger Effect" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Dunning-Kruger Effect</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'arj7oStGLkU' }" @click="selectExampleVideo('arj7oStGLkU')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg" alt="Procrastination TED Talk" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Procrastination TED Talk</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'KpVPST_P4W8' }" @click="selectExampleVideo('KpVPST_P4W8')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg" alt="Quantum Computing" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Quantum Computing</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'pqWUuYTcG-o' }" @click="selectExampleVideo('pqWUuYTcG-o')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg" alt="Business Growth" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Business Growth</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'UF8uR6Z6KLc' }" @click="selectExampleVideo('UF8uR6Z6KLc')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/UF8uR6Z6KLc/hqdefault.jpg" alt="Steve Jobs Stanford" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Steve Jobs Stanford</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Design Option 3: Step-by-step with numbers -->
          <div class="design-option-3" style="display: none;">
            <div class="steps steps-horizontal mb-6">
              <div class="step step-primary">Choose Source</div>
              <div class="step">Process</div>
              <div class="step">Results</div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div class="option-card border-2 border-primary bg-primary/5 cursor-pointer" onclick="selectStep3('your-video')">
                <div class="flex items-center mb-3">
                  <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</div>
                  <h3 class="font-bold">Your Video</h3>
                </div>
                <div class="main-input-container">
                  <input type="text" v-model="videoUrl" placeholder="Paste YouTube URL here" class="input input-bordered w-full" />
                </div>
              </div>

              <div class="option-card border-2 border-secondary bg-secondary/5 cursor-pointer" onclick="selectStep3('examples')">
                <div class="flex items-center mb-3">
                  <div class="w-8 h-8 bg-secondary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</div>
                  <h3 class="font-bold">Try Examples</h3>
                </div>
                <p class="text-sm text-base-content/70">Click to see examples</p>
              </div>
            </div>

            <div id="step3-examples" style="display: none;">
              <div class="grid grid-cols-2 md:grid-cols-5 gap-3 max-w-4xl mx-auto">
                <!-- Example videos for option 3 -->
                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'TT81fe2IobI' }" @click="selectExampleVideo('TT81fe2IobI')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg" alt="Dunning-Kruger Effect" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Dunning-Kruger Effect</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'arj7oStGLkU' }" @click="selectExampleVideo('arj7oStGLkU')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg" alt="Procrastination TED Talk" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Procrastination TED Talk</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'KpVPST_P4W8' }" @click="selectExampleVideo('KpVPST_P4W8')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg" alt="Quantum Computing" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Quantum Computing</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'pqWUuYTcG-o' }" @click="selectExampleVideo('pqWUuYTcG-o')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg" alt="Business Growth" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Business Growth</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'UF8uR6Z6KLc' }" @click="selectExampleVideo('UF8uR6Z6KLc')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/UF8uR6Z6KLc/hqdefault.jpg" alt="Steve Jobs Stanford" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Steve Jobs Stanford</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Design Option 4: Toggle switch style -->
          <div class="design-option-4" style="display: none;">
            <div class="form-control mb-6">
              <label class="label cursor-pointer justify-center">
                <span class="label-text mr-4 font-medium">Your Video</span>
                <input type="checkbox" class="toggle toggle-primary" onchange="toggleMode4(this)" />
                <span class="label-text ml-4 font-medium">Examples</span>
              </label>
            </div>

            <div id="toggle4-your-video" class="input-mode-container">
              <div class="main-input-container">
                <div class="flex flex-col sm:flex-row gap-3">
                  <input type="text" v-model="videoUrl" placeholder="Paste YouTube URL here" class="input input-bordered input-lg w-full" />
                  <button class="btn btn-primary btn-lg font-bold" @click="processUrl">Transcribe now</button>
                </div>
              </div>
            </div>

            <div id="toggle4-examples" class="input-mode-container" style="display: none;">
              <div class="grid grid-cols-2 md:grid-cols-5 gap-3 max-w-4xl mx-auto">
                <!-- Example videos for option 4 -->
                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'TT81fe2IobI' }" @click="selectExampleVideo('TT81fe2IobI')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg" alt="Dunning-Kruger Effect" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Dunning-Kruger Effect</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'arj7oStGLkU' }" @click="selectExampleVideo('arj7oStGLkU')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg" alt="Procrastination TED Talk" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Procrastination TED Talk</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'KpVPST_P4W8' }" @click="selectExampleVideo('KpVPST_P4W8')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg" alt="Quantum Computing" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Quantum Computing</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'pqWUuYTcG-o' }" @click="selectExampleVideo('pqWUuYTcG-o')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg" alt="Business Growth" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Business Growth</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'UF8uR6Z6KLc' }" @click="selectExampleVideo('UF8uR6Z6KLc')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/UF8uR6Z6KLc/hqdefault.jpg" alt="Steve Jobs Stanford" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Steve Jobs Stanford</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Design Option 5: Radio button cards -->
          <div class="design-option-5" style="display: none;">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <label class="radio-card cursor-pointer">
                <input type="radio" name="input-method-5" class="radio radio-primary" checked onchange="switchRadio5('your-video')" />
                <div class="card bg-base-100 border-2 border-primary shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                      <div>
                        <h3 class="font-bold">Add Your Video</h3>
                        <p class="text-sm text-base-content/70">Paste any YouTube URL</p>
                      </div>
                    </div>
                  </div>
                </div>
              </label>

              <label class="radio-card cursor-pointer">
                <input type="radio" name="input-method-5" class="radio radio-secondary" onchange="switchRadio5('examples')" />
                <div class="card bg-base-100 border-2 border-base-300 shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      <div>
                        <h3 class="font-bold">Try Examples</h3>
                        <p class="text-sm text-base-content/70">Test with sample videos</p>
                      </div>
                    </div>
                  </div>
                </div>
              </label>
            </div>

            <div id="radio5-your-video" class="content-section">
              <div class="main-input-container">
                <div class="flex flex-col sm:flex-row gap-3">
                  <input type="text" v-model="videoUrl" placeholder="Paste YouTube URL here" class="input input-bordered input-lg w-full" />
                  <button class="btn btn-primary btn-lg font-bold" @click="processUrl">Transcribe now</button>
                </div>
              </div>
            </div>

            <div id="radio5-examples" class="content-section" style="display: none;">
              <div class="grid grid-cols-2 md:grid-cols-5 gap-3 max-w-4xl mx-auto">
                <!-- Example videos for option 5 -->
                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'TT81fe2IobI' }" @click="selectExampleVideo('TT81fe2IobI')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/TT81fe2IobI/hqdefault.jpg" alt="Dunning-Kruger Effect" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Dunning-Kruger Effect</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'arj7oStGLkU' }" @click="selectExampleVideo('arj7oStGLkU')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/arj7oStGLkU/hqdefault.jpg" alt="Procrastination TED Talk" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Procrastination TED Talk</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'KpVPST_P4W8' }" @click="selectExampleVideo('KpVPST_P4W8')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/KpVPST_P4W8/hqdefault.jpg" alt="Quantum Computing" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Quantum Computing</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'pqWUuYTcG-o' }" @click="selectExampleVideo('pqWUuYTcG-o')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/pqWUuYTcG-o/hqdefault.jpg" alt="Business Growth" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Business Growth</p>
                </div>

                <div class="example-video-card" :class="{ 'selected': selectedVideoId === 'UF8uR6Z6KLc' }" @click="selectExampleVideo('UF8uR6Z6KLc')">
                  <div class="video-thumbnail">
                    <img src="https://i.ytimg.com/vi/UF8uR6Z6KLc/hqdefault.jpg" alt="Steve Jobs Stanford" class="w-full h-full object-cover" />
                    <div class="play-overlay">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="video-title">Steve Jobs Stanford</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Bottom Info -->
          <div class="flex justify-center mt-4">
            <div class="text-sm text-base-content/70 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>No Sign-up required</span>
            </div>
          </div>

          <div v-if="error" class="alert alert-error mt-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{{ error }}</span>
          </div>
        </div>
      </div>

      <!-- Social Proof -->
      <div class="social-proof-section">
        <div class="flex flex-col md:flex-row items-center justify-center gap-6 text-sm text-base-content/60">
          <!-- User Avatars -->
          <div class="flex items-center gap-3">
            <div class="flex -space-x-2">
              <div class="avatar">
                <div class="w-8 h-8 rounded-full ring-2 ring-white">
                  <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" />
                </div>
              </div>
              <div class="avatar">
                <div class="w-8 h-8 rounded-full ring-2 ring-white">
                  <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User avatar" />
                </div>
              </div>
              <div class="avatar">
                <div class="w-8 h-8 rounded-full ring-2 ring-white">
                  <img src="https://randomuser.me/api/portraits/men/86.jpg" alt="User avatar" />
                </div>
              </div>
              <div class="avatar">
                <div class="w-8 h-8 rounded-full ring-2 ring-white">
                  <img src="https://randomuser.me/api/portraits/women/63.jpg" alt="User avatar" />
                </div>
              </div>
              <div class="avatar">
                <div class="w-8 h-8 rounded-full ring-2 ring-white">
                  <img src="https://randomuser.me/api/portraits/men/54.jpg" alt="User avatar" />
                </div>
              </div>
            </div>
            <div class="text-sm">
              Trusted by <span class="font-semibold">12k+ users</span>
            </div>
          </div>

          <!-- Rating -->
          <div class="flex items-center gap-2">
            <div class="flex text-yellow-400">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </div>
            <span class="font-semibold">4.78</span>
          </div>

          <!-- Usage Stats -->
          <div class="text-sm">
            <span class="font-semibold">1M+ videos</span> transcribed
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
// Temporary function to switch between design options
window.showDesign = function(option) {
  // Hide all designs
  for (let i = 1; i <= 5; i++) {
    const element = document.querySelector(`.design-option-${i}`);
    if (element) {
      element.style.display = 'none';
    }
  }

  // Show selected design
  const selectedElement = document.querySelector(`.design-option-${option}`);
  if (selectedElement) {
    selectedElement.style.display = 'block';
  }
}

// Option 2: Tab switching
window.switchTab2 = function(tab, element) {
  // Update tab active states
  const tabs = document.querySelectorAll('.design-option-2 .tab');
  tabs.forEach(t => t.classList.remove('tab-active'));
  element.classList.add('tab-active');

  // Show/hide content
  if (tab === 'your-video') {
    document.getElementById('tab2-your-video').style.display = 'block';
    document.getElementById('tab2-examples').style.display = 'none';
  } else {
    document.getElementById('tab2-your-video').style.display = 'none';
    document.getElementById('tab2-examples').style.display = 'block';
  }
}

// Option 3: Step selection
window.selectStep3 = function(step) {
  if (step === 'examples') {
    document.getElementById('step3-examples').style.display = 'block';
  } else {
    document.getElementById('step3-examples').style.display = 'none';
  }
}

// Option 4: Toggle switch
window.toggleMode4 = function(toggle) {
  if (toggle.checked) {
    // Show examples
    document.getElementById('toggle4-your-video').style.display = 'none';
    document.getElementById('toggle4-examples').style.display = 'block';
  } else {
    // Show your video
    document.getElementById('toggle4-your-video').style.display = 'block';
    document.getElementById('toggle4-examples').style.display = 'none';
  }
}

// Option 5: Radio card selection
window.switchRadio5 = function(mode) {
  // Update card borders
  const cards = document.querySelectorAll('.design-option-5 .card');
  cards.forEach(card => {
    card.classList.remove('border-primary', 'border-secondary');
    card.classList.add('border-base-300');
  });

  if (mode === 'your-video') {
    document.getElementById('radio5-your-video').style.display = 'block';
    document.getElementById('radio5-examples').style.display = 'none';
    // Update selected card
    const selectedCard = document.querySelector('.design-option-5 input[type="radio"]:checked').nextElementSibling;
    selectedCard.classList.remove('border-base-300');
    selectedCard.classList.add('border-primary');
  } else {
    document.getElementById('radio5-your-video').style.display = 'none';
    document.getElementById('radio5-examples').style.display = 'block';
    // Update selected card
    const selectedCard = document.querySelector('.design-option-5 input[type="radio"]:checked').nextElementSibling;
    selectedCard.classList.remove('border-base-300');
    selectedCard.classList.add('border-secondary');
  }
}
</script>

<style scoped>
/* Hero Section Styles */
.hero-section {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(147, 51, 234, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);
  border-radius: 1.5rem;
  padding: 2rem 1.5rem;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(147, 51, 234, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.hero-section > * {
  position: relative;
  z-index: 1;
}

/* Transformation Process */
.transformation-process {
  position: relative;
}

.video-input-visual {
  text-align: center;
}

.video-thumbnail {
  width: 4rem;
  height: 3rem;
  background: linear-gradient(135deg, #ff0000, #cc0000);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.2);
}

.transform-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  margin: 0 auto;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Content Items - No hover effects */
.content-item {
  text-align: center;
  padding: 0.75rem 0.5rem;
}

.content-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
}

/* Input Section */
.input-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Section Headers */
.section-header h3 {
  background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--s)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced Input Container */
.main-input-container {
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(79, 70, 229, 0.1);
  border-radius: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.main-input-container:hover {
  border-color: rgba(79, 70, 229, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.main-input-container:focus-within {
  border-color: hsl(var(--p));
  box-shadow: 0 8px 24px rgba(79, 70, 229, 0.15);
  transform: translateY(-2px);
}

/* Design Options Styles */

/* Option 1: Card-based with badges */
.input-card {
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.card-badge {
  position: absolute;
  top: -0.5rem;
  right: 1rem;
}

/* Option 2: Tab-style */
.design-option-2 .tabs {
  justify-content: center;
}

/* Option 3: Step-by-step */
.option-card {
  padding: 1.5rem;
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.option-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Option 4: Toggle switch */
.input-mode-container {
  transition: all 0.3s ease;
}

/* Option 5: Radio button cards */
.radio-card {
  display: block;
}

.radio-card input[type="radio"] {
  position: absolute;
  opacity: 0;
}

.radio-card input[type="radio"]:checked + .card {
  border-color: hsl(var(--p));
  background: rgba(79, 70, 229, 0.05);
}

.radio-card:hover .card {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Example Videos */
.example-video-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.75rem;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.example-video-card.selected {
  border-color: hsl(var(--p));
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.3);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 60px;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #f3f4f6;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.example-video-card:hover .play-overlay {
  opacity: 1;
}

.video-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  text-align: center;
  margin-top: 0.5rem;
  line-height: 1.2;
  padding: 0 0.25rem;
}

/* Social Proof Animation */
.social-proof-section {
  animation: fadeInUp 0.8s ease-out 0.5s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .transformation-process .flex {
    flex-direction: column;
    gap: 2rem;
  }

  .input-section {
    order: 1;
  }

  .arrow-section {
    order: 2;
  }

  .output-section {
    order: 3;
  }

  .transform-arrow svg {
    transform: rotate(90deg);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 1.5rem 1rem;
  }

  .content-grid .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .content-icon {
    width: 1.75rem;
    height: 1.75rem;
  }

  .content-label {
    font-size: 0.7rem;
  }

  .input-section {
    padding: 1rem;
  }

  .example-videos-section .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .main-input-container {
    padding: 0.75rem;
  }

  .video-thumbnail {
    height: 50px;
  }

  .video-title {
    font-size: 0.7rem;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .example-videos-section .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {
  .video-thumbnail {
    width: 3rem;
    height: 2.25rem;
  }

  .transform-arrow {
    width: 2rem;
    height: 2rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .input-section {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
  }

  .content-label {
    color: #d1d5db;
  }

  .example-video-card {
    background: rgba(31, 41, 55, 0.9);
    border-color: rgba(75, 85, 99, 0.3);
  }

  .example-video-card.selected {
    border-color: hsl(var(--p));
  }

  .video-title {
    color: #d1d5db;
  }
}
</style>
