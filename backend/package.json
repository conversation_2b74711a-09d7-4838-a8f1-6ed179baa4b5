{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "postinstall": "node scripts/install-yt-dlp.js", "install-yt-dlp": "node scripts/install-yt-dlp.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.9.0", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "googleapis": "^148.0.0", "he": "^1.2.0", "node-webvtt": "^1.9.4", "youtube-transcript": "^1.2.1", "youtube-transcript-api": "^1.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}