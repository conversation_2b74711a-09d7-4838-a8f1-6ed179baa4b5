const NodeCache = require('node-cache');

// Monitoring cache for tracking metrics
const metricsCache = new NodeCache({ stdTTL: 3600 }); // 1 hour TTL
const alertCache = new NodeCache({ stdTTL: 300 }); // 5 minutes for alert throttling

// Monitoring configuration
const MONITORING_CONFIG = {
  // Alert thresholds
  MAX_ERRORS_PER_MINUTE: 10,
  MAX_AI_FAILURES_PER_HOUR: 5,
  MAX_CONCURRENT_REQUESTS: 50,
  HIGH_RESPONSE_TIME_THRESHOLD: 5000, // 5 seconds
  
  // Metrics collection intervals
  METRICS_WINDOW: 60000, // 1 minute
  CLEANUP_INTERVAL: 300000, // 5 minutes
};

// Metrics storage
let metrics = {
  requests: {
    total: 0,
    successful: 0,
    failed: 0,
    byEndpoint: {},
    byIP: {},
  },
  ai: {
    requests: 0,
    successful: 0,
    failed: 0,
    totalTokens: 0,
    totalCost: 0,
  },
  performance: {
    averageResponseTime: 0,
    slowRequests: 0,
    concurrentRequests: 0,
  },
  security: {
    rateLimitHits: 0,
    invalidRequests: 0,
    suspiciousActivity: [],
  },
  errors: {
    total: 0,
    byType: {},
    recent: [],
  }
};

/**
 * Get current timestamp for metrics
 */
function getTimestamp() {
  return new Date().toISOString();
}

/**
 * Get client IP address
 */
function getClientIP(req) {
  return req.ip || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1';
}

/**
 * Log security event
 */
function logSecurityEvent(type, details, req) {
  const event = {
    timestamp: getTimestamp(),
    type,
    details,
    ip: getClientIP(req),
    userAgent: req.get('User-Agent'),
    endpoint: req.path,
    method: req.method,
  };
  
  metrics.security.suspiciousActivity.push(event);
  
  // Keep only last 100 security events
  if (metrics.security.suspiciousActivity.length > 100) {
    metrics.security.suspiciousActivity = metrics.security.suspiciousActivity.slice(-100);
  }
  
  console.warn('🚨 Security Event:', JSON.stringify(event, null, 2));
  
  // Check for alert conditions
  checkSecurityAlerts(type, event);
}

/**
 * Log error event
 */
function logError(error, req, context = {}) {
  const errorEvent = {
    timestamp: getTimestamp(),
    message: error.message,
    stack: error.stack,
    ip: getClientIP(req),
    endpoint: req?.path,
    method: req?.method,
    context,
  };
  
  metrics.errors.total++;
  metrics.errors.recent.push(errorEvent);
  
  // Keep only last 50 errors
  if (metrics.errors.recent.length > 50) {
    metrics.errors.recent = metrics.errors.recent.slice(-50);
  }
  
  // Count by error type
  const errorType = error.constructor.name || 'Unknown';
  metrics.errors.byType[errorType] = (metrics.errors.byType[errorType] || 0) + 1;
  
  console.error('💥 Application Error:', JSON.stringify(errorEvent, null, 2));
  
  // Check for error rate alerts
  checkErrorRateAlerts();
}

/**
 * Track AI request metrics
 */
function trackAIRequest(success, tokens = 0, cost = 0, model = 'unknown') {
  metrics.ai.requests++;
  
  if (success) {
    metrics.ai.successful++;
    metrics.ai.totalTokens += tokens;
    metrics.ai.totalCost += cost;
  } else {
    metrics.ai.failed++;
  }
  
  // Log AI usage
  console.log(`🤖 AI Request: ${success ? 'SUCCESS' : 'FAILED'}, Model: ${model}, Tokens: ${tokens}, Cost: $${cost.toFixed(4)}`);
  
  // Check AI failure rate
  if (!success) {
    checkAIFailureAlerts();
  }
}

/**
 * Track request performance
 */
function trackPerformance(responseTime, endpoint) {
  // Update average response time (simple moving average)
  const totalRequests = metrics.requests.total;
  metrics.performance.averageResponseTime = 
    (metrics.performance.averageResponseTime * totalRequests + responseTime) / (totalRequests + 1);
  
  // Track slow requests
  if (responseTime > MONITORING_CONFIG.HIGH_RESPONSE_TIME_THRESHOLD) {
    metrics.performance.slowRequests++;
    console.warn(`🐌 Slow Request: ${endpoint} took ${responseTime}ms`);
  }
}

/**
 * Check for security alerts
 */
function checkSecurityAlerts(type, event) {
  const alertKey = `security_${type}_${event.ip}`;
  
  // Throttle alerts (max 1 per 5 minutes per IP per type)
  if (alertCache.get(alertKey)) {
    return;
  }
  
  // Set alert throttle
  alertCache.set(alertKey, true, 300);
  
  // Log critical security events
  if (type === 'rate_limit_exceeded' || type === 'invalid_input' || type === 'cost_limit_exceeded') {
    console.error(`🚨 SECURITY ALERT: ${type} from IP ${event.ip}`);
    
    // In production, you might want to send this to a monitoring service
    // sendToMonitoringService('security_alert', event);
  }
}

/**
 * Check for error rate alerts
 */
function checkErrorRateAlerts() {
  const now = Date.now();
  const oneMinuteAgo = now - 60000;
  
  // Count errors in the last minute
  const recentErrors = metrics.errors.recent.filter(
    error => new Date(error.timestamp).getTime() > oneMinuteAgo
  );
  
  if (recentErrors.length > MONITORING_CONFIG.MAX_ERRORS_PER_MINUTE) {
    const alertKey = 'error_rate_high';
    
    if (!alertCache.get(alertKey)) {
      alertCache.set(alertKey, true, 300);
      console.error(`🚨 HIGH ERROR RATE ALERT: ${recentErrors.length} errors in the last minute`);
      
      // In production, send to monitoring service
      // sendToMonitoringService('high_error_rate', { count: recentErrors.length, errors: recentErrors });
    }
  }
}

/**
 * Check for AI failure alerts
 */
function checkAIFailureAlerts() {
  const now = Date.now();
  const oneHourAgo = now - 3600000;
  
  // This is a simplified check - in production you'd want more sophisticated tracking
  if (metrics.ai.failed > MONITORING_CONFIG.MAX_AI_FAILURES_PER_HOUR) {
    const alertKey = 'ai_failure_rate_high';
    
    if (!alertCache.get(alertKey)) {
      alertCache.set(alertKey, true, 300);
      console.error(`🚨 HIGH AI FAILURE RATE ALERT: ${metrics.ai.failed} failures`);
      
      // In production, send to monitoring service
      // sendToMonitoringService('high_ai_failure_rate', { failures: metrics.ai.failed });
    }
  }
}

/**
 * Request tracking middleware
 */
const requestTracker = (req, res, next) => {
  const startTime = Date.now();
  const clientIP = getClientIP(req);
  
  // Track concurrent requests
  metrics.performance.concurrentRequests++;
  
  // Track request by IP
  metrics.requests.byIP[clientIP] = (metrics.requests.byIP[clientIP] || 0) + 1;
  
  // Track request by endpoint
  const endpoint = req.path;
  metrics.requests.byEndpoint[endpoint] = (metrics.requests.byEndpoint[endpoint] || 0) + 1;
  
  // Override res.end to capture response metrics
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Update metrics
    metrics.requests.total++;
    metrics.performance.concurrentRequests--;
    
    if (res.statusCode >= 200 && res.statusCode < 400) {
      metrics.requests.successful++;
    } else {
      metrics.requests.failed++;
    }
    
    // Track performance
    trackPerformance(responseTime, endpoint);
    
    // Log request
    console.log(`📊 ${req.method} ${endpoint} - ${res.statusCode} - ${responseTime}ms - ${clientIP}`);
    
    // Call original end
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Get current metrics
 */
function getMetrics() {
  return {
    ...metrics,
    timestamp: getTimestamp(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  };
}

/**
 * Reset metrics (useful for testing or periodic resets)
 */
function resetMetrics() {
  metrics = {
    requests: {
      total: 0,
      successful: 0,
      failed: 0,
      byEndpoint: {},
      byIP: {},
    },
    ai: {
      requests: 0,
      successful: 0,
      failed: 0,
      totalTokens: 0,
      totalCost: 0,
    },
    performance: {
      averageResponseTime: 0,
      slowRequests: 0,
      concurrentRequests: 0,
    },
    security: {
      rateLimitHits: 0,
      invalidRequests: 0,
      suspiciousActivity: [],
    },
    errors: {
      total: 0,
      byType: {},
      recent: [],
    }
  };
}

/**
 * Cleanup old metrics periodically
 */
function startMetricsCleanup() {
  setInterval(() => {
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    
    // Clean up old security events
    metrics.security.suspiciousActivity = metrics.security.suspiciousActivity.filter(
      event => new Date(event.timestamp).getTime() > oneHourAgo
    );
    
    // Clean up old errors
    metrics.errors.recent = metrics.errors.recent.filter(
      error => new Date(error.timestamp).getTime() > oneHourAgo
    );
    
    console.log('🧹 Cleaned up old metrics');
  }, MONITORING_CONFIG.CLEANUP_INTERVAL);
}

// Start cleanup process
startMetricsCleanup();

module.exports = {
  requestTracker,
  logSecurityEvent,
  logError,
  trackAIRequest,
  trackPerformance,
  getMetrics,
  resetMetrics,
  MONITORING_CONFIG
};
