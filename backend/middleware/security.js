const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const helmet = require('helmet');
const NodeCache = require('node-cache');
const validator = require('validator');

// Cache for rate limiting (IP-based tracking)
const rateLimitCache = new NodeCache({ stdTTL: 86400 }); // 24 hours
const costTrackingCache = new NodeCache({ stdTTL: 3600 }); // 1 hour for cost tracking

// Security configuration
const SECURITY_CONFIG = {
  // Rate limiting
  MAX_VIDEOS_PER_DAY: 12,
  MAX_AI_REQUESTS_PER_HOUR: 20,
  MAX_REQUESTS_PER_MINUTE: 30,
  
  // Request limits
  MAX_REQUEST_SIZE: '10mb',
  MAX_TRANSCRIPT_LENGTH: 50000, // characters
  
  // Timeouts
  REQUEST_TIMEOUT: 30000, // 30 seconds
  AI_REQUEST_TIMEOUT: 60000, // 60 seconds
  
  // Cost protection
  MAX_AI_TOKENS_PER_HOUR: 100000,
  MAX_AI_COST_PER_HOUR: 5.00, // USD
};

/**
 * Get client IP address, handling proxies
 */
function getClientIP(req) {
  return req.ip || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1';
}

/**
 * Validate YouTube video ID
 */
function isValidYouTubeVideoId(videoId) {
  if (!videoId || typeof videoId !== 'string') return false;
  
  // YouTube video IDs are exactly 11 characters, alphanumeric plus - and _
  const youtubeIdRegex = /^[a-zA-Z0-9_-]{11}$/;
  return youtubeIdRegex.test(videoId);
}

/**
 * Validate and sanitize language code
 */
function isValidLanguageCode(lang) {
  if (!lang || typeof lang !== 'string') return false;
  
  // ISO 639-1 language codes (2 letters) or extended codes like 'en-US'
  const langRegex = /^[a-z]{2}(-[A-Z]{2})?$/;
  return langRegex.test(lang) && lang.length <= 5;
}

/**
 * Basic security headers middleware
 */
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "http:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://www.googleapis.com", "https://generativelanguage.googleapis.com"],
      frameSrc: ["'self'", "https://www.youtube.com"],
    },
  },
  crossOriginEmbedderPolicy: false, // Allow YouTube embeds
});

/**
 * General rate limiting middleware
 */
const generalRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getClientIP,
});

/**
 * Video processing rate limiting (12 videos per day per IP)
 */
const videoRateLimit = rateLimit({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: SECURITY_CONFIG.MAX_VIDEOS_PER_DAY,
  message: {
    error: 'Daily video limit exceeded. You can process up to 12 videos per day.',
    limit: SECURITY_CONFIG.MAX_VIDEOS_PER_DAY,
    retryAfter: '24 hours'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getClientIP,
  // Custom store using NodeCache for persistence
  store: {
    incr: (key) => {
      const current = rateLimitCache.get(key) || 0;
      const newValue = current + 1;
      rateLimitCache.set(key, newValue, 86400); // 24 hours TTL
      return Promise.resolve({ totalHits: newValue, resetTime: new Date(Date.now() + 86400000) });
    },
    decrement: (key) => {
      const current = rateLimitCache.get(key) || 0;
      const newValue = Math.max(0, current - 1);
      rateLimitCache.set(key, newValue, 86400);
      return Promise.resolve();
    },
    resetKey: (key) => {
      rateLimitCache.del(key);
      return Promise.resolve();
    },
  },
});

/**
 * AI request rate limiting (20 AI requests per hour per IP)
 */
const aiRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: SECURITY_CONFIG.MAX_AI_REQUESTS_PER_HOUR,
  message: {
    error: 'AI request limit exceeded. You can make up to 20 AI-powered requests per hour.',
    limit: SECURITY_CONFIG.MAX_AI_REQUESTS_PER_HOUR,
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: getClientIP,
});

/**
 * Slow down middleware for suspicious activity
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 10, // Allow 10 requests per 15 minutes at full speed
  delayMs: () => 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 5000, // Maximum delay of 5 seconds
  keyGenerator: getClientIP,
  validate: { delayMs: false }, // Disable warning
});

/**
 * Input validation middleware
 */
const validateInput = (req, res, next) => {
  try {
    // Validate video ID in URL params
    if (req.params.videoId) {
      if (!isValidYouTubeVideoId(req.params.videoId)) {
        return res.status(400).json({
          error: 'Invalid YouTube video ID format',
          details: 'Video ID must be exactly 11 characters (alphanumeric, dash, underscore)'
        });
      }
    }

    // Validate language parameter
    if (req.query.lang) {
      if (!isValidLanguageCode(req.query.lang)) {
        return res.status(400).json({
          error: 'Invalid language code format',
          details: 'Language code must be in ISO 639-1 format (e.g., "en", "es", "en-US")'
        });
      }
    }

    // Validate request body for POST requests
    if (req.method === 'POST' && req.body) {
      // Check for excessively large prompts
      if (req.body.prompt && req.body.prompt.length > SECURITY_CONFIG.MAX_TRANSCRIPT_LENGTH) {
        return res.status(400).json({
          error: 'Request payload too large',
          details: `Prompt exceeds maximum length of ${SECURITY_CONFIG.MAX_TRANSCRIPT_LENGTH} characters`
        });
      }

      // Validate video ID in request body
      if (req.body.videoId && !isValidYouTubeVideoId(req.body.videoId)) {
        return res.status(400).json({
          error: 'Invalid YouTube video ID in request body'
        });
      }

      // Sanitize string inputs
      if (req.body.prompt && typeof req.body.prompt === 'string') {
        req.body.prompt = validator.escape(req.body.prompt);
      }
    }

    next();
  } catch (error) {
    console.error('Input validation error:', error);
    res.status(400).json({
      error: 'Invalid request format'
    });
  }
};

/**
 * Cost protection middleware for AI requests
 */
const costProtection = (req, res, next) => {
  try {
    const clientIP = getClientIP(req);
    const hourKey = `cost_${clientIP}_${Math.floor(Date.now() / 3600000)}`;
    
    const currentCost = costTrackingCache.get(hourKey) || 0;
    
    // Estimate cost based on request type
    let estimatedCost = 0;
    if (req.path.includes('/summary')) estimatedCost = 0.01;
    else if (req.path.includes('/quotes')) estimatedCost = 0.02;
    else if (req.path.includes('/description')) estimatedCost = 0.015;
    else if (req.path.includes('/social-media')) estimatedCost = 0.025;
    else if (req.path.includes('/anki-cards')) estimatedCost = 0.03;
    else if (req.path.includes('/gemini/generate')) estimatedCost = 0.01;
    
    if (currentCost + estimatedCost > SECURITY_CONFIG.MAX_AI_COST_PER_HOUR) {
      return res.status(429).json({
        error: 'AI usage cost limit exceeded for this hour',
        details: `Maximum cost per hour: $${SECURITY_CONFIG.MAX_AI_COST_PER_HOUR}`,
        retryAfter: '1 hour'
      });
    }
    
    // Track the cost
    costTrackingCache.set(hourKey, currentCost + estimatedCost, 3600);
    
    next();
  } catch (error) {
    console.error('Cost protection error:', error);
    next(); // Continue on error to avoid blocking legitimate requests
  }
};

/**
 * Request timeout middleware
 */
const requestTimeout = (timeout = SECURITY_CONFIG.REQUEST_TIMEOUT) => {
  return (req, res, next) => {
    req.setTimeout(timeout, () => {
      res.status(408).json({
        error: 'Request timeout',
        details: `Request exceeded ${timeout}ms timeout`
      });
    });
    next();
  };
};

/**
 * Error sanitization middleware
 */
const sanitizeErrors = (err, req, res, next) => {
  console.error('Application error:', err);
  
  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(err.status || 500).json({
    error: err.message || 'Internal server error',
    details: isDevelopment ? err.stack : undefined,
    timestamp: new Date().toISOString(),
    requestId: req.id || 'unknown'
  });
};

module.exports = {
  securityHeaders,
  generalRateLimit,
  videoRateLimit,
  aiRateLimit,
  speedLimiter,
  validateInput,
  costProtection,
  requestTimeout,
  sanitizeErrors,
  isValidYouTubeVideoId,
  isValidLanguageCode,
  getClientIP,
  SECURITY_CONFIG
};
